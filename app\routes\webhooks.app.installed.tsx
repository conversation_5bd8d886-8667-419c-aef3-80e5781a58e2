import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import db from "../db.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { shop, session, topic } = await authenticate.webhook(request);

  console.log(`Received ${topic} webhook for ${shop}`);

  try {
    // Check if this shop had a previous subscription
    const existingSubscription = await (db as any).subscription.findUnique({
      where: { shop }
    });

    if (existingSubscription) {
      console.log(`Found existing subscription for shop ${shop}:`, existingSubscription);
      
      // If the subscription was previously uninstalled, mark it as available for reactivation
      if (existingSubscription.status === 'uninstalled') {
        await (db as any).subscription.update({
          where: { shop },
          data: { 
            status: 'expired', // Set to expired so merchant can resubscribe
            updatedAt: new Date()
          }
        });
        console.log(`Marked subscription as expired for reactivation for shop: ${shop}`);
      }
      
      // If subscription was active/cancelled, check with Shopify for current status
      if (['active', 'cancelled', 'pending'].includes(existingSubscription.status)) {
        // We'll check the actual Shopify subscription status in the billing service
        console.log(`Shop ${shop} reinstalled with existing ${existingSubscription.status} subscription`);
      }
    } else {
      console.log(`No existing subscription found for shop ${shop} - new installation`);
    }

    return json({ 
      status: "success",
      message: "App installation processed successfully",
      shop,
      hasExistingSubscription: !!existingSubscription
    });

  } catch (error) {
    console.error("Error processing app installation webhook:", error);
    return json({ 
      error: "Internal server error",
      shop 
    }, { status: 500 });
  }
};
