import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { getSubscription } from "../services/billing.server";

/**
 * API endpoint to check charge status
 * GET /api/billing/charge-status
 */
export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const { session } = await authenticate.admin(request);
    const subscription = await getSubscription(session.shop);

    if (!subscription) {
      return json({
        status: 'no_subscription',
        message: 'No subscription found'
      });
    }

    return json({
      status: subscription.status,
      subscriptionId: subscription.subscriptionId,
      chargeId: subscription.chargeId,
      currentPeriodEnd: subscription.currentPeriodEnd,
      message: getStatusMessage(subscription.status)
    });

  } catch (error) {
    console.error("Error checking charge status:", error);
    return json({
      error: "Failed to check charge status"
    }, { status: 500 });
  }
};

/**
 * API endpoint to handle charge actions (accept, decline, request approval)
 * POST /api/billing/charge-status
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const { admin, session } = await authenticate.admin(request);
    const formData = await request.formData();
    const action = formData.get("action") as string;
    const chargeId = formData.get("chargeId") as string;

    switch (action) {
      case "accept":
        return await handleChargeAcceptance(admin, session.shop, chargeId);
      
      case "decline":
        return await handleChargeDecline(admin, session.shop, chargeId);
      
      case "request_approval":
        return await handleChargeApprovalRequest(admin, session.shop);
      
      default:
        return json({
          error: "Invalid action"
        }, { status: 400 });
    }

  } catch (error) {
    console.error("Error handling charge action:", error);
    return json({
      error: "Failed to process charge action"
    }, { status: 500 });
  }
};

async function handleChargeAcceptance(admin: any, shop: string, chargeId: string) {
  // In Shopify's billing system, charges are typically accepted automatically
  // when the merchant approves them through the confirmation URL
  // This endpoint can be used to verify the acceptance
  
  try {
    const response = await admin.graphql(
      `#graphql
        query getAppSubscription($id: ID!) {
          node(id: $id) {
            ... on AppSubscription {
              id
              status
              name
              currentPeriodEnd
            }
          }
        }`,
      {
        variables: { id: chargeId }
      }
    );

    const responseJson = await response.json();
    const subscription = responseJson.data?.node;

    if (subscription && subscription.status === 'ACTIVE') {
      return json({
        status: 'accepted',
        message: 'Charge accepted successfully',
        subscription
      });
    }

    return json({
      status: 'pending',
      message: 'Charge is still pending approval'
    });

  } catch (error) {
    console.error("Error accepting charge:", error);
    return json({
      error: "Failed to accept charge"
    }, { status: 500 });
  }
}

async function handleChargeDecline(admin: any, shop: string, chargeId: string) {
  try {
    const response = await admin.graphql(
      `#graphql
        mutation appSubscriptionCancel($id: ID!) {
          appSubscriptionCancel(id: $id) {
            appSubscription {
              id
              status
            }
            userErrors {
              field
              message
            }
          }
        }`,
      {
        variables: { id: chargeId }
      }
    );

    const responseJson = await response.json();
    
    if (responseJson.data?.appSubscriptionCancel?.userErrors?.length > 0) {
      return json({
        error: responseJson.data.appSubscriptionCancel.userErrors[0].message
      }, { status: 400 });
    }

    return json({
      status: 'declined',
      message: 'Charge declined successfully'
    });

  } catch (error) {
    console.error("Error declining charge:", error);
    return json({
      error: "Failed to decline charge"
    }, { status: 500 });
  }
}

async function handleChargeApprovalRequest(admin: any, shop: string) {
  // This would typically redirect to create a new subscription
  // or provide the confirmation URL for an existing pending subscription
  
  return json({
    status: 'approval_required',
    message: 'Please use the subscription creation flow to request approval',
    redirectTo: '/app/billing'
  });
}

function getStatusMessage(status: string): string {
  switch (status) {
    case 'active':
      return 'Subscription is active';
    case 'pending':
      return 'Subscription is pending approval';
    case 'expired':
      return 'Subscription has expired';
    case 'cancelled':
      return 'Subscription was cancelled';
    case 'uninstalled':
      return 'App was uninstalled - please resubscribe';
    case 'trial':
      return 'Currently in trial period';
    default:
      return 'Unknown subscription status';
  }
}
