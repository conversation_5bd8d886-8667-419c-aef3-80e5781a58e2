import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { checkAndRecoverSubscriptionOnReinstall } from "../services/billing.server";

/**
 * API endpoint to recover subscription on app reinstall
 * POST /api/billing/recover-subscription
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const { session } = await authenticate.admin(request);
    
    console.log(`Attempting to recover subscription for shop: ${session.shop}`);
    
    const result = await checkAndRecoverSubscriptionOnReinstall(request, session.shop);
    
    if (result.recovered) {
      return json({
        success: true,
        recovered: true,
        subscription: result.subscription,
        message: result.message
      });
    } else {
      return json({
        success: true,
        recovered: false,
        message: result.message
      });
    }

  } catch (error) {
    console.error("Error recovering subscription:", error);
    return json({
      success: false,
      error: "Failed to recover subscription",
      message: "An error occurred while trying to recover your subscription"
    }, { status: 500 });
  }
};
