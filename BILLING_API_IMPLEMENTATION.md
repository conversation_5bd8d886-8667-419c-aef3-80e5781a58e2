# Billing API Implementation for App Reinstallation

This document describes the comprehensive billing API implementation that handles charge acceptance, decline, and resubscription on app reinstall according to Shopify requirements.

## Problem Statement

When a Shopify app with charges is uninstalled and then reinstalled, merchants should be able to:
1. Recover their previous active subscription if it still exists in Shopify
2. Resubscribe to the app if their previous subscription was cancelled
3. Have a seamless experience without losing access to paid features

## Solution Overview

The implementation includes:

### 1. Webhook Handlers

#### App Installation Webhook (`webhooks.app.installed.tsx`)
- Detects when the app is installed/reinstalled
- Checks for existing subscription data
- Marks uninstalled subscriptions as available for reactivation

#### App Uninstallation Webhook (`webhooks.app.uninstalled.tsx`)
- Preserves subscription data instead of deleting it
- Marks subscriptions as 'uninstalled' status
- Allows for potential recovery on reinstall

### 2. Billing Service Enhancements (`services/billing.server.ts`)

#### New Functions:
- `checkAndRecoverSubscriptionOnReinstall()`: Checks Shopify for active subscriptions and recovers them
- Enhanced `checkSubscriptionStatus()`: Handles 'uninstalled' status with appropriate messaging

#### New Status: 'uninstalled'
- Indicates app was previously uninstalled but subscription data is preserved
- Allows merchants to understand they can recover or resubscribe

### 3. Billing API Endpoints

#### Charge Status API (`api.billing.charge-status.tsx`)
- GET: Check current charge/subscription status
- POST: Handle charge actions (accept, decline, request approval)
- Provides programmatic access to billing state

#### Subscription Recovery API (`api.billing.recover-subscription.tsx`)
- POST: Attempt to recover subscription from Shopify on reinstall
- Returns success/failure status and recovered subscription data

### 4. Enhanced Billing UI (`app.billing.tsx`)

#### New Features:
- Detects 'uninstalled' status and shows appropriate messaging
- "Try to Recover Previous Subscription" button for reinstalled apps
- Clear distinction between recovery and new subscription creation
- Success/failure feedback for recovery attempts

### 5. Database Schema Updates

#### Subscription Model:
- Added 'uninstalled' status to preserve data on app uninstall
- Maintains subscription history for potential recovery

## Implementation Flow

### Install → Uninstall → Reinstall Flow

1. **Initial Installation**
   - App creates trial subscription
   - User can upgrade to paid subscription

2. **App Uninstallation**
   - `app/uninstalled` webhook triggered
   - Sessions deleted (security)
   - Subscription marked as 'uninstalled' (preserved for recovery)

3. **App Reinstallation**
   - `app/installed` webhook triggered
   - System checks for existing subscription data
   - If found with 'uninstalled' status, marks as 'expired' for reactivation

4. **Subscription Recovery**
   - User sees "Welcome back" message with recovery option
   - Recovery API checks Shopify for active subscriptions
   - If found, automatically recovers and activates subscription
   - If not found, user can create new subscription

## API Endpoints

### GET /api/billing/charge-status
Returns current subscription status and charge information.

**Response:**
```json
{
  "status": "active|pending|expired|cancelled|uninstalled",
  "subscriptionId": "gid://shopify/AppSubscription/123",
  "chargeId": "charge_123",
  "currentPeriodEnd": "2024-12-31T23:59:59Z",
  "message": "Status description"
}
```

### POST /api/billing/charge-status
Handle charge actions.

**Request:**
```json
{
  "action": "accept|decline|request_approval",
  "chargeId": "charge_123"
}
```

### POST /api/billing/recover-subscription
Attempt subscription recovery on reinstall.

**Response:**
```json
{
  "success": true,
  "recovered": true,
  "subscription": { ... },
  "message": "Active subscription recovered successfully!"
}
```

## Testing

Comprehensive test suite includes:

### Unit Tests (`tests/billing.test.ts`)
- Billing service functions
- Subscription status checking
- Recovery logic
- Error handling

### Webhook Tests (`tests/webhooks.test.ts`)
- Install/uninstall webhook handlers
- Database state management
- Error scenarios

### Integration Tests
- Complete install → uninstall → reinstall flow
- Recovery scenarios (success/failure)
- Edge cases and error conditions

### Running Tests
```bash
npm run test          # Run tests in watch mode
npm run test:run      # Run tests once
npm run test:coverage # Run with coverage report
```

## Key Benefits

1. **Merchant Experience**: Seamless resubscription after reinstall
2. **Revenue Protection**: Prevents loss of active subscriptions
3. **Compliance**: Meets Shopify Billing API requirements
4. **Data Integrity**: Preserves subscription history
5. **Error Handling**: Graceful fallbacks for edge cases

## Security Considerations

- Webhook signature verification for all billing webhooks
- Proper authentication for all API endpoints
- Session cleanup on uninstall while preserving billing data
- Validation of subscription ownership before recovery

## Monitoring and Logging

- Comprehensive logging for all billing operations
- Error tracking for failed recovery attempts
- Audit trail for subscription state changes
- Performance monitoring for API endpoints

This implementation ensures that merchants can seamlessly resubscribe to the app after reinstallation, meeting Shopify's requirements for apps with charges while providing an excellent user experience.
