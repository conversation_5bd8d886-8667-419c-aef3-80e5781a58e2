import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  getSubscription, 
  createTrialSubscription, 
  checkSubscriptionStatus,
  checkAndRecoverSubscriptionOnReinstall
} from '../app/services/billing.server';

// Mock Prisma
const mockPrisma = {
  subscription: {
    findUnique: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    upsert: vi.fn(),
    updateMany: vi.fn()
  }
};

// Mock authenticate
const mockAuthenticate = {
  admin: vi.fn()
};

// Mock admin GraphQL
const mockAdmin = {
  graphql: vi.fn()
};

vi.mock('../app/db.server', () => ({
  default: mockPrisma
}));

vi.mock('../app/shopify.server', () => ({
  authenticate: mockAuthenticate
}));

describe('Billing Service', () => {
  const testShop = 'test-shop.myshopify.com';
  
  beforeEach(() => {
    vi.clearAllMocks();
    mockAuthenticate.admin.mockResolvedValue({ admin: mockAdmin });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('getSubscription', () => {
    it('should return subscription if exists', async () => {
      const mockSubscription = {
        id: '1',
        shop: testShop,
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.subscription.findUnique.mockResolvedValue(mockSubscription);

      const result = await getSubscription(testShop);
      expect(result).toEqual(mockSubscription);
      expect(mockPrisma.subscription.findUnique).toHaveBeenCalledWith({
        where: { shop: testShop }
      });
    });

    it('should return null if subscription does not exist', async () => {
      mockPrisma.subscription.findUnique.mockResolvedValue(null);

      const result = await getSubscription(testShop);
      expect(result).toBeNull();
    });

    it('should handle database errors gracefully', async () => {
      mockPrisma.subscription.findUnique.mockRejectedValue(new Error('Database error'));

      const result = await getSubscription(testShop);
      expect(result).toBeNull();
    });
  });

  describe('createTrialSubscription', () => {
    it('should create a new trial subscription', async () => {
      const mockCreatedSubscription = {
        id: '1',
        shop: testShop,
        status: 'trial',
        trialEndsAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.subscription.create.mockResolvedValue(mockCreatedSubscription);

      const result = await createTrialSubscription(testShop);
      expect(result).toEqual(mockCreatedSubscription);
      expect(mockPrisma.subscription.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          shop: testShop,
          status: 'trial',
          trialEndsAt: expect.any(Date),
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date)
        })
      });
    });
  });

  describe('checkSubscriptionStatus', () => {
    it('should create trial for new shop', async () => {
      mockPrisma.subscription.findUnique.mockResolvedValue(null);
      mockPrisma.subscription.create.mockResolvedValue({
        id: '1',
        shop: testShop,
        status: 'trial',
        trialEndsAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        createdAt: new Date(),
        updatedAt: new Date()
      });

      const result = await checkSubscriptionStatus(testShop);
      
      expect(result.hasAccess).toBe(true);
      expect(result.status).toBe('trial');
      expect(result.daysLeft).toBe(7);
    });

    it('should handle active subscription', async () => {
      const mockSubscription = {
        id: '1',
        shop: testShop,
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.subscription.findUnique.mockResolvedValue(mockSubscription);

      const result = await checkSubscriptionStatus(testShop);
      
      expect(result.hasAccess).toBe(true);
      expect(result.status).toBe('active');
      expect(result.message).toBe('Subscription active');
    });

    it('should handle expired subscription', async () => {
      const mockSubscription = {
        id: '1',
        shop: testShop,
        status: 'expired',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.subscription.findUnique.mockResolvedValue(mockSubscription);

      const result = await checkSubscriptionStatus(testShop);
      
      expect(result.hasAccess).toBe(false);
      expect(result.status).toBe('expired');
      expect(result.message).toBe('Subscription required to use this app.');
    });

    it('should handle uninstalled subscription', async () => {
      const mockSubscription = {
        id: '1',
        shop: testShop,
        status: 'uninstalled',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.subscription.findUnique.mockResolvedValue(mockSubscription);

      const result = await checkSubscriptionStatus(testShop);
      
      expect(result.hasAccess).toBe(false);
      expect(result.status).toBe('uninstalled');
      expect(result.message).toBe('Welcome back! Please resubscribe to continue using the app.');
    });

    it('should handle expired trial', async () => {
      const expiredDate = new Date(Date.now() - 24 * 60 * 60 * 1000); // Yesterday
      const mockSubscription = {
        id: '1',
        shop: testShop,
        status: 'trial',
        trialEndsAt: expiredDate,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.subscription.findUnique.mockResolvedValue(mockSubscription);
      mockPrisma.subscription.update.mockResolvedValue({
        ...mockSubscription,
        status: 'expired'
      });

      const result = await checkSubscriptionStatus(testShop);
      
      expect(result.hasAccess).toBe(false);
      expect(result.status).toBe('expired');
      expect(mockPrisma.subscription.update).toHaveBeenCalledWith({
        where: { shop: testShop },
        data: { status: 'expired', updatedAt: expect.any(Date) }
      });
    });
  });

  describe('checkAndRecoverSubscriptionOnReinstall', () => {
    const mockRequest = new Request('http://localhost');

    it('should recover active subscription from Shopify', async () => {
      const mockShopifyResponse = {
        data: {
          currentAppInstallation: {
            activeSubscriptions: [{
              id: 'gid://shopify/AppSubscription/123',
              status: 'ACTIVE',
              name: 'Test Plan',
              currentPeriodEnd: '2024-12-31T23:59:59Z'
            }]
          }
        }
      };

      mockAdmin.graphql.mockResolvedValue({
        json: () => Promise.resolve(mockShopifyResponse)
      });

      const mockRecoveredSubscription = {
        id: '1',
        shop: testShop,
        subscriptionId: 'gid://shopify/AppSubscription/123',
        status: 'active',
        currentPeriodEnd: new Date('2024-12-31T23:59:59Z'),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.subscription.upsert.mockResolvedValue(mockRecoveredSubscription);

      const result = await checkAndRecoverSubscriptionOnReinstall(mockRequest, testShop);
      
      expect(result.recovered).toBe(true);
      expect(result.subscription).toEqual(mockRecoveredSubscription);
      expect(result.message).toBe('Active subscription recovered successfully!');
    });

    it('should handle no active subscriptions', async () => {
      const mockShopifyResponse = {
        data: {
          currentAppInstallation: {
            activeSubscriptions: []
          }
        }
      };

      mockAdmin.graphql.mockResolvedValue({
        json: () => Promise.resolve(mockShopifyResponse)
      });

      const result = await checkAndRecoverSubscriptionOnReinstall(mockRequest, testShop);
      
      expect(result.recovered).toBe(false);
      expect(result.message).toBe('No active subscription found to recover.');
    });

    it('should handle GraphQL errors', async () => {
      mockAdmin.graphql.mockRejectedValue(new Error('GraphQL error'));

      const result = await checkAndRecoverSubscriptionOnReinstall(mockRequest, testShop);
      
      expect(result.recovered).toBe(false);
      expect(result.message).toBe('Error checking subscription status.');
    });
  });

  describe('Integration: Install/Uninstall/Reinstall Flow', () => {
    const mockRequest = new Request('http://localhost');

    it('should handle complete install -> uninstall -> reinstall flow', async () => {
      // Step 1: New installation - should create trial
      mockPrisma.subscription.findUnique.mockResolvedValueOnce(null);
      mockPrisma.subscription.create.mockResolvedValueOnce({
        id: '1',
        shop: testShop,
        status: 'trial',
        trialEndsAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        createdAt: new Date(),
        updatedAt: new Date()
      });

      let result = await checkSubscriptionStatus(testShop);
      expect(result.status).toBe('trial');
      expect(result.hasAccess).toBe(true);

      // Step 2: User subscribes
      const activeSubscription = {
        id: '1',
        shop: testShop,
        status: 'active',
        subscriptionId: 'gid://shopify/AppSubscription/123',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.subscription.findUnique.mockResolvedValueOnce(activeSubscription);
      result = await checkSubscriptionStatus(testShop);
      expect(result.status).toBe('active');
      expect(result.hasAccess).toBe(true);

      // Step 3: App uninstalled - subscription marked as uninstalled
      mockPrisma.subscription.updateMany.mockResolvedValueOnce({ count: 1 });
      // Simulate uninstall webhook processing

      // Step 4: App reinstalled - subscription should be recoverable
      const uninstalledSubscription = {
        id: '1',
        shop: testShop,
        status: 'uninstalled',
        subscriptionId: 'gid://shopify/AppSubscription/123',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.subscription.findUnique.mockResolvedValueOnce(uninstalledSubscription);
      result = await checkSubscriptionStatus(testShop);
      expect(result.status).toBe('uninstalled');
      expect(result.hasAccess).toBe(false);
      expect(result.message).toBe('Welcome back! Please resubscribe to continue using the app.');

      // Step 5: Try to recover subscription
      const mockShopifyResponse = {
        data: {
          currentAppInstallation: {
            activeSubscriptions: [{
              id: 'gid://shopify/AppSubscription/123',
              status: 'ACTIVE',
              name: 'Test Plan',
              currentPeriodEnd: '2024-12-31T23:59:59Z'
            }]
          }
        }
      };

      mockAdmin.graphql.mockResolvedValueOnce({
        json: () => Promise.resolve(mockShopifyResponse)
      });

      const recoveredSubscription = {
        ...uninstalledSubscription,
        status: 'active',
        currentPeriodEnd: new Date('2024-12-31T23:59:59Z')
      };

      mockPrisma.subscription.upsert.mockResolvedValueOnce(recoveredSubscription);

      const recoveryResult = await checkAndRecoverSubscriptionOnReinstall(mockRequest, testShop);
      expect(recoveryResult.recovered).toBe(true);
      expect(recoveryResult.message).toBe('Active subscription recovered successfully!');

      // Step 6: Verify subscription is now active again
      mockPrisma.subscription.findUnique.mockResolvedValueOnce(recoveredSubscription);
      result = await checkSubscriptionStatus(testShop);
      expect(result.status).toBe('active');
      expect(result.hasAccess).toBe(true);
    });

    it('should handle reinstall when no active subscription exists in Shopify', async () => {
      // Subscription was uninstalled
      const uninstalledSubscription = {
        id: '1',
        shop: testShop,
        status: 'uninstalled',
        subscriptionId: 'gid://shopify/AppSubscription/123',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // No active subscriptions in Shopify
      const mockShopifyResponse = {
        data: {
          currentAppInstallation: {
            activeSubscriptions: []
          }
        }
      };

      mockAdmin.graphql.mockResolvedValueOnce({
        json: () => Promise.resolve(mockShopifyResponse)
      });

      const recoveryResult = await checkAndRecoverSubscriptionOnReinstall(mockRequest, testShop);
      expect(recoveryResult.recovered).toBe(false);
      expect(recoveryResult.message).toBe('No active subscription found to recover.');

      // User should be able to create new subscription
      mockPrisma.subscription.findUnique.mockResolvedValueOnce(uninstalledSubscription);
      const result = await checkSubscriptionStatus(testShop);
      expect(result.status).toBe('uninstalled');
      expect(result.hasAccess).toBe(false);
    });
  });
});
