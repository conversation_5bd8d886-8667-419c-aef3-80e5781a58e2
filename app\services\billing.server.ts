import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import { SUBSCRIPTION_CONFIG } from "../config/subscription";

export interface SubscriptionData {
  id?: string;
  shop: string;
  status: 'active' | 'expired' | 'trial' | 'cancelled' | 'pending';
  trialEndsAt?: Date;
  currentPeriodEnd?: Date;
  subscriptionId?: string;
  chargeId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export async function getSubscription(shop: string): Promise<SubscriptionData | null> {
  try {
    const subscription = await (prisma as any).subscription.findUnique({
      where: { shop }
    });
    
    return subscription as SubscriptionData | null;
  } catch (error) {
    console.error("Error getting subscription:", error);
    return null;
  }
}

export async function createTrialSubscription(shop: string): Promise<SubscriptionData> {
  const trialEndsAt = new Date();
  trialEndsAt.setDate(trialEndsAt.getDate() + SUBSCRIPTION_CONFIG.trialDays);

  const subscription = await (prisma as any).subscription.upsert({
    where: { shop },
    update: {
      status: 'trial',
      trialEndsAt,
      updatedAt: new Date()
    },
    create: {
      shop,
      status: 'trial',
      trialEndsAt,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });

  return subscription as SubscriptionData;
}

/**
 * Check for existing active subscriptions in Shopify to prevent double charging
 */
export async function checkExistingShopifySubscriptions(request: Request): Promise<{
  hasActiveSubscription: boolean;
  subscriptions: any[];
}> {
  try {
    const { admin } = await authenticate.admin(request);

    const response = await admin.graphql(
      `#graphql
        query appSubscriptions {
          currentAppInstallation {
            activeSubscriptions {
              id
              status
              name
              lineItems {
                plan {
                  pricingDetails {
                    ... on AppRecurringPricing {
                      price {
                        amount
                        currencyCode
                      }
                    }
                  }
                }
              }
            }
          }
        }`
    );

    const responseJson = await response.json();
    const subscriptions = responseJson.data?.currentAppInstallation?.activeSubscriptions || [];

    return {
      hasActiveSubscription: subscriptions.length > 0,
      subscriptions: subscriptions
    };
  } catch (error) {
    console.error("Error checking existing Shopify subscriptions:", error);
    return {
      hasActiveSubscription: false,
      subscriptions: []
    };
  }
}

export async function createRecurringCharge(request: Request, shop: string) {
  const { admin } = await authenticate.admin(request);

  try {
    // CRITICAL: Check for existing subscriptions first
    const existingLocalSubscription = await getSubscription(shop);
    
    if (existingLocalSubscription && (existingLocalSubscription.status === 'active' || existingLocalSubscription.status === 'pending')) {
      throw new Error(`Cannot create subscription: existing ${existingLocalSubscription.status} subscription found`);
    }

    const { hasActiveSubscription, subscriptions } = await checkExistingShopifySubscriptions(request);
    
    if (hasActiveSubscription) {
      console.log("Found existing active Shopify subscriptions:", subscriptions);
      throw new Error("Cannot create subscription: active subscriptions already exist in Shopify");
    }

    const response = await admin.graphql(
      `#graphql
        mutation appSubscriptionCreate($name: String!, $lineItems: [AppSubscriptionLineItemInput!]!, $returnUrl: URL!) {
          appSubscriptionCreate(name: $name, lineItems: $lineItems, returnUrl: $returnUrl) {
            appSubscription {
              id
              status
            }
            confirmationUrl
            userErrors {
              field
              message
            }
          }
        }`,
      {
        variables: {
          name: SUBSCRIPTION_CONFIG.planName,
          returnUrl: `${process.env.SHOPIFY_APP_URL}/app/billing/callback`,
          lineItems: [
            {
              plan: {
                appRecurringPricingDetails: {
                  price: { amount: SUBSCRIPTION_CONFIG.monthlyPrice.toString(), currencyCode: "USD" }
                }
              }
            }
          ]
        }
      }
    );

    const responseJson = await response.json();
    
    if (responseJson.data?.appSubscriptionCreate?.userErrors?.length > 0) {
      throw new Error(responseJson.data.appSubscriptionCreate.userErrors[0].message);
    }

    const subscription = responseJson.data?.appSubscriptionCreate?.appSubscription;
    const confirmationUrl = responseJson.data?.appSubscriptionCreate?.confirmationUrl;

    if (subscription?.id) {
      // Update subscription in database
      await (prisma as any).subscription.upsert({
        where: { shop },
        update: {
          subscriptionId: subscription.id,
          status: 'pending',
          updatedAt: new Date()
        },
        create: {
          shop,
          subscriptionId: subscription.id,
          status: 'pending',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }

    return { confirmationUrl, subscription };
  } catch (error) {
    console.error("Error creating recurring charge:", error);
    throw error;
  }
}

export async function checkSubscriptionStatus(shop: string): Promise<{
  hasAccess: boolean;
  status: string;
  daysLeft?: number;
  message?: string;
}> {
  const subscription = await getSubscription(shop);

  if (!subscription) {
    // New installation - create trial
    await createTrialSubscription(shop);
    return {
      hasAccess: true,
      status: 'trial',
      daysLeft: SUBSCRIPTION_CONFIG.trialDays,
      message: `Welcome! You have ${SUBSCRIPTION_CONFIG.trialDays} days of free trial.`
    };
  }

  const now = new Date();

  switch (subscription.status) {
    case 'trial':
      if (subscription.trialEndsAt && now <= subscription.trialEndsAt) {
        const daysLeft = Math.ceil((subscription.trialEndsAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        return {
          hasAccess: true,
          status: 'trial',
          daysLeft,
          message: daysLeft > 1 ? `${daysLeft} days left in trial` : 'Trial ends today'
        };
      } else {
        // Trial expired
        await (prisma as any).subscription.update({
          where: { shop },
          data: { status: 'expired', updatedAt: new Date() }
        });
        return {
          hasAccess: false,
          status: 'expired',
          message: 'Trial expired. Please subscribe to continue using the app.'
        };
      }

    case 'active':
      return {
        hasAccess: true,
        status: 'active',
        message: 'Subscription active'
      };

    case 'pending':
      return {
        hasAccess: false,
        status: 'pending',
        message: 'Subscription pending approval. Please complete the subscription process.'
      };

    case 'expired':
    case 'cancelled':
    case 'uninstalled':
      return {
        hasAccess: false,
        status: subscription.status,
        message: subscription.status === 'uninstalled'
          ? 'Welcome back! Please resubscribe to continue using the app.'
          : 'Subscription required to use this app.'
      };

    default:
      return {
        hasAccess: false,
        status: 'unknown',
        message: 'Please contact support.'
      };
  }
}

/**
 * Check and recover subscription status on app reinstall
 * This function checks if there are active subscriptions in Shopify that we should recover
 */
export async function checkAndRecoverSubscriptionOnReinstall(request: Request, shop: string): Promise<{
  recovered: boolean;
  subscription?: any;
  message: string;
}> {
  try {
    const { admin } = await authenticate.admin(request);

    // Check for active subscriptions in Shopify
    const response = await admin.graphql(
      `#graphql
        query appSubscriptions {
          currentAppInstallation {
            activeSubscriptions {
              id
              status
              name
              createdAt
              currentPeriodEnd
              lineItems {
                plan {
                  pricingDetails {
                    ... on AppRecurringPricing {
                      price {
                        amount
                        currencyCode
                      }
                    }
                  }
                }
              }
            }
          }
        }`
    );

    const responseJson = await response.json();
    const activeSubscriptions = responseJson.data?.currentAppInstallation?.activeSubscriptions || [];

    if (activeSubscriptions.length > 0) {
      // Found active subscription in Shopify, recover it
      const shopifySubscription = activeSubscriptions[0];

      const recoveredSubscription = await (prisma as any).subscription.upsert({
        where: { shop },
        update: {
          subscriptionId: shopifySubscription.id,
          status: shopifySubscription.status === 'ACTIVE' ? 'active' : 'pending',
          currentPeriodEnd: shopifySubscription.currentPeriodEnd ? new Date(shopifySubscription.currentPeriodEnd) : null,
          updatedAt: new Date()
        },
        create: {
          shop,
          subscriptionId: shopifySubscription.id,
          status: shopifySubscription.status === 'ACTIVE' ? 'active' : 'pending',
          currentPeriodEnd: shopifySubscription.currentPeriodEnd ? new Date(shopifySubscription.currentPeriodEnd) : null,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      console.log(`Recovered subscription for shop ${shop}:`, recoveredSubscription);

      return {
        recovered: true,
        subscription: recoveredSubscription,
        message: 'Active subscription recovered successfully!'
      };
    }

    return {
      recovered: false,
      message: 'No active subscription found to recover.'
    };

  } catch (error) {
    console.error("Error checking for subscription recovery:", error);
    return {
      recovered: false,
      message: 'Error checking subscription status.'
    };
  }
}

export async function cancelSubscription(request: Request, shop: string) {
  const subscription = await getSubscription(shop);
  
  if (!subscription?.subscriptionId) {
    throw new Error("No active subscription found");
  }

  const { admin } = await authenticate.admin(request);

  try {
    const response = await admin.graphql(
      `#graphql
        mutation appSubscriptionCancel($id: ID!) {
          appSubscriptionCancel(id: $id) {
            appSubscription {
              id
              status
            }
            userErrors {
              field
              message
            }
          }
        }`,
      {
        variables: {
          id: subscription.subscriptionId
        }
      }
    );

    const responseJson = await response.json();
    
    if (responseJson.data?.appSubscriptionCancel?.userErrors?.length > 0) {
      throw new Error(responseJson.data.appSubscriptionCancel.userErrors[0].message);
    }

    // Update subscription status in database
    await (prisma as any).subscription.update({
      where: { shop },
      data: { 
        status: 'cancelled',
        updatedAt: new Date()
      }
    });

    return true;
  } catch (error) {
    console.error("Error cancelling subscription:", error);
    throw error;
  }
} 