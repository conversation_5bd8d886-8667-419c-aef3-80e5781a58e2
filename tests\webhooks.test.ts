import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

// Mock Prisma
const mockPrisma = {
  session: {
    deleteMany: vi.fn()
  },
  subscription: {
    findUnique: vi.fn(),
    update: vi.fn(),
    updateMany: vi.fn()
  }
};

// Mock authenticate
const mockAuthenticate = {
  webhook: vi.fn()
};

vi.mock('../app/db.server', () => ({
  default: mockPrisma
}));

vi.mock('../app/shopify.server', () => ({
  authenticate: mockAuthenticate
}));

describe('Webhook Handlers', () => {
  const testShop = 'test-shop.myshopify.com';
  const mockSession = { id: 'session-123', shop: testShop };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('App Uninstalled Webhook', () => {
    it('should mark subscription as uninstalled and delete sessions', async () => {
      mockAuthenticate.webhook.mockResolvedValue({
        shop: testShop,
        session: mockSession,
        topic: 'app/uninstalled'
      });

      mockPrisma.session.deleteMany.mockResolvedValue({ count: 1 });
      mockPrisma.subscription.updateMany.mockResolvedValue({ count: 1 });

      // Import and test the webhook handler
      const { action } = await import('../app/routes/webhooks.app.uninstalled');
      const mockRequest = new Request('http://localhost', { method: 'POST' });

      const response = await action({ request: mockRequest });

      expect(response).toBeInstanceOf(Response);
      expect(mockPrisma.session.deleteMany).toHaveBeenCalledWith({
        where: { shop: testShop }
      });
      expect(mockPrisma.subscription.updateMany).toHaveBeenCalledWith({
        where: { shop: testShop },
        data: {
          status: 'uninstalled',
          updatedAt: expect.any(Date)
        }
      });
    });

    it('should handle case when session is null', async () => {
      mockAuthenticate.webhook.mockResolvedValue({
        shop: testShop,
        session: null,
        topic: 'app/uninstalled'
      });

      mockPrisma.subscription.updateMany.mockResolvedValue({ count: 1 });

      const { action } = await import('../app/routes/webhooks.app.uninstalled');
      const mockRequest = new Request('http://localhost', { method: 'POST' });

      const response = await action({ request: mockRequest });

      expect(response).toBeInstanceOf(Response);
      expect(mockPrisma.session.deleteMany).not.toHaveBeenCalled();
      expect(mockPrisma.subscription.updateMany).toHaveBeenCalledWith({
        where: { shop: testShop },
        data: {
          status: 'uninstalled',
          updatedAt: expect.any(Date)
        }
      });
    });

    it('should handle database errors gracefully', async () => {
      mockAuthenticate.webhook.mockResolvedValue({
        shop: testShop,
        session: mockSession,
        topic: 'app/uninstalled'
      });

      mockPrisma.session.deleteMany.mockResolvedValue({ count: 1 });
      mockPrisma.subscription.updateMany.mockRejectedValue(new Error('Database error'));

      const { action } = await import('../app/routes/webhooks.app.uninstalled');
      const mockRequest = new Request('http://localhost', { method: 'POST' });

      const response = await action({ request: mockRequest });

      expect(response).toBeInstanceOf(Response);
      // Should still complete successfully even if subscription update fails
    });
  });

  describe('App Installed Webhook', () => {
    it('should handle new installation', async () => {
      mockAuthenticate.webhook.mockResolvedValue({
        shop: testShop,
        session: mockSession,
        topic: 'app/installed'
      });

      mockPrisma.subscription.findUnique.mockResolvedValue(null);

      const { action } = await import('../app/routes/webhooks.app.installed');
      const mockRequest = new Request('http://localhost', { method: 'POST' });

      const response = await action({ request: mockRequest });
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.status).toBe('success');
      expect(responseData.hasExistingSubscription).toBe(false);
      expect(mockPrisma.subscription.findUnique).toHaveBeenCalledWith({
        where: { shop: testShop }
      });
    });

    it('should handle reinstallation with uninstalled subscription', async () => {
      mockAuthenticate.webhook.mockResolvedValue({
        shop: testShop,
        session: mockSession,
        topic: 'app/installed'
      });

      const existingSubscription = {
        id: '1',
        shop: testShop,
        status: 'uninstalled',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.subscription.findUnique.mockResolvedValue(existingSubscription);
      mockPrisma.subscription.update.mockResolvedValue({
        ...existingSubscription,
        status: 'expired'
      });

      const { action } = await import('../app/routes/webhooks.app.installed');
      const mockRequest = new Request('http://localhost', { method: 'POST' });

      const response = await action({ request: mockRequest });
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.status).toBe('success');
      expect(responseData.hasExistingSubscription).toBe(true);
      expect(mockPrisma.subscription.update).toHaveBeenCalledWith({
        where: { shop: testShop },
        data: {
          status: 'expired',
          updatedAt: expect.any(Date)
        }
      });
    });

    it('should handle reinstallation with active subscription', async () => {
      mockAuthenticate.webhook.mockResolvedValue({
        shop: testShop,
        session: mockSession,
        topic: 'app/installed'
      });

      const existingSubscription = {
        id: '1',
        shop: testShop,
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.subscription.findUnique.mockResolvedValue(existingSubscription);

      const { action } = await import('../app/routes/webhooks.app.installed');
      const mockRequest = new Request('http://localhost', { method: 'POST' });

      const response = await action({ request: mockRequest });
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.status).toBe('success');
      expect(responseData.hasExistingSubscription).toBe(true);
      // Should not update active subscription
      expect(mockPrisma.subscription.update).not.toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      mockAuthenticate.webhook.mockResolvedValue({
        shop: testShop,
        session: mockSession,
        topic: 'app/installed'
      });

      mockPrisma.subscription.findUnique.mockRejectedValue(new Error('Database error'));

      const { action } = await import('../app/routes/webhooks.app.installed');
      const mockRequest = new Request('http://localhost', { method: 'POST' });

      const response = await action({ request: mockRequest });
      const responseData = await response.json();

      expect(response.status).toBe(500);
      expect(responseData.error).toBe('Internal server error');
    });
  });
});
