import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testSubscriptionSystem() {
  console.log('🧪 Testing simplified subscription system...');
  
  try {
    const testShop = 'test-shop.myshopify.com';
    
    // Test 1: Check non-existent shop
    console.log('\n1️⃣ Testing non-existent shop...');
    const nonExistent = await prisma.subscription.findUnique({
      where: { shop: 'non-existent.myshopify.com' }
    });
    console.log('Non-existent shop result:', nonExistent);
    
    // Test 2: Create active subscription
    console.log('\n2️⃣ Creating active subscription...');
    const activeSubscription = await prisma.subscription.upsert({
      where: { shop: testShop },
      update: {
        isActive: true,
        updatedAt: new Date(),
      },
      create: {
        shop: testShop,
        isActive: true,
      },
    });
    console.log('Created subscription:', {
      shop: activeSubscription.shop,
      isActive: activeSubscription.isActive,
    });
    
    // Test 3: Check if shop is subscribed
    console.log('\n3️⃣ Checking subscription status...');
    const isSubscribed = await prisma.subscription.findUnique({
      where: { shop: testShop },
      select: { isActive: true }
    });
    console.log(`${testShop} is subscribed:`, isSubscribed?.isActive);
    
    // Test 4: Deactivate subscription
    console.log('\n4️⃣ Deactivating subscription...');
    await prisma.subscription.update({
      where: { shop: testShop },
      data: { isActive: false }
    });
    
    const deactivated = await prisma.subscription.findUnique({
      where: { shop: testShop },
      select: { isActive: true }
    });
    console.log(`${testShop} after deactivation:`, deactivated?.isActive);
    
    // Test 5: Get all subscriptions
    console.log('\n5️⃣ Getting all subscriptions...');
    const allSubscriptions = await prisma.subscription.findMany({
      select: {
        shop: true,
        isActive: true,
        createdAt: true,
      },
      orderBy: { createdAt: 'desc' }
    });
    console.log('All subscriptions:', allSubscriptions);
    
    // Test 6: Count active vs inactive
    console.log('\n6️⃣ Counting subscriptions...');
    const activeCount = await prisma.subscription.count({
      where: { isActive: true }
    });
    const inactiveCount = await prisma.subscription.count({
      where: { isActive: false }
    });
    console.log(`Active: ${activeCount}, Inactive: ${inactiveCount}`);
    
    // Clean up test data
    console.log('\n🧹 Cleaning up test data...');
    await prisma.subscription.delete({
      where: { shop: testShop }
    });
    
    console.log('✅ All tests passed! Subscription system is working correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testSubscriptionSystem()
  .catch((error) => {
    console.error('Test failed:', error);
    process.exit(1);
  });
