import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { authenticate } from "~/shopify.server";
import { 
  isShopSubscribed, 
  getSubscriptionStatus, 
  activateSubscription, 
  deactivateSubscription,
  startTrial 
} from "~/utils/subscription.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  // Simple check: is shop subscribed?
  const isSubscribed = await isShopSubscribed(shop);
  
  // Detailed status if needed
  const subscriptionStatus = await getSubscriptionStatus(shop);

  return json({
    shop,
    isSubscribed,
    subscriptionStatus,
  });
}

export async function action({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  
  const formData = await request.formData();
  const action = formData.get("action");

  switch (action) {
    case "activate":
      await activateSubscription(shop, {
        subscriptionId: "shopify-subscription-id",
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      });
      break;
      
    case "deactivate":
      await deactivateSubscription(shop);
      break;
      
    case "start_trial":
      await startTrial(shop, 7); // 7 day trial
      break;
  }

  return json({ success: true });
}

export default function SubscriptionExample() {
  const { shop, isSubscribed, subscriptionStatus } = useLoaderData<typeof loader>();

  return (
    <div style={{ padding: "20px" }}>
      <h1>Subscription Status for {shop}</h1>
      
      <div style={{ marginBottom: "20px" }}>
        <h2>Simple Status Check:</h2>
        <p>
          <strong>Is Subscribed:</strong> 
          <span style={{ 
            color: isSubscribed ? "green" : "red",
            fontWeight: "bold" 
          }}>
            {isSubscribed ? "✅ YES" : "❌ NO"}
          </span>
        </p>
      </div>

      <div style={{ marginBottom: "20px" }}>
        <h2>Detailed Status:</h2>
        <ul>
          <li><strong>Active:</strong> {subscriptionStatus.isActive ? "Yes" : "No"}</li>
          <li><strong>Has Valid Trial:</strong> {subscriptionStatus.hasValidTrial ? "Yes" : "No"}</li>
          <li><strong>Has Valid Subscription:</strong> {subscriptionStatus.hasValidSubscription ? "Yes" : "No"}</li>
          {subscriptionStatus.trialEndsAt && (
            <li><strong>Trial Ends:</strong> {new Date(subscriptionStatus.trialEndsAt).toLocaleDateString()}</li>
          )}
          {subscriptionStatus.currentPeriodEnd && (
            <li><strong>Subscription Ends:</strong> {new Date(subscriptionStatus.currentPeriodEnd).toLocaleDateString()}</li>
          )}
        </ul>
      </div>

      <div>
        <h2>Actions:</h2>
        <form method="post" style={{ display: "flex", gap: "10px" }}>
          <button type="submit" name="action" value="activate">
            Activate Subscription
          </button>
          <button type="submit" name="action" value="deactivate">
            Deactivate Subscription
          </button>
          <button type="submit" name="action" value="start_trial">
            Start 7-Day Trial
          </button>
        </form>
      </div>

      <div style={{ marginTop: "30px", padding: "15px", backgroundColor: "#f5f5f5" }}>
        <h3>Usage in Your Code:</h3>
        <pre style={{ fontSize: "12px" }}>
{`// Simple check anywhere in your app:
const isSubscribed = await isShopSubscribed(shop);
if (!isSubscribed) {
  return redirect("/upgrade");
}

// In your route loaders:
export async function loader({ request }) {
  const { session } = await authenticate.admin(request);
  const isSubscribed = await isShopSubscribed(session.shop);
  
  return json({ 
    canAccessFeature: isSubscribed,
    // ... other data
  });
}`}
        </pre>
      </div>
    </div>
  );
}
