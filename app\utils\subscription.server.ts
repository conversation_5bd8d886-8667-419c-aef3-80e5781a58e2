import prisma from "~/db.server";

export interface SubscriptionStatus {
  isActive: boolean;
  hasValidTrial: boolean;
  hasValidSubscription: boolean;
  trialEndsAt?: Date | null;
  currentPeriodEnd?: Date | null;
}

/**
 * Get subscription status for a shop - simple binary logic
 */
export async function getSubscriptionStatus(shop: string): Promise<SubscriptionStatus> {
  const subscription = await prisma.subscription.findUnique({
    where: { shop },
    select: {
      isActive: true,
      trialEndsAt: true,
      currentPeriodEnd: true,
    },
  });

  if (!subscription) {
    return {
      isActive: false,
      hasValidTrial: false,
      hasValidSubscription: false,
    };
  }

  const now = new Date();
  const hasValidTrial = subscription.trialEndsAt ? subscription.trialEndsAt > now : false;
  const hasValidSubscription = subscription.currentPeriodEnd ? subscription.currentPeriodEnd > now : false;

  return {
    isActive: subscription.isActive && (hasValidTrial || hasValidSubscription),
    hasValidTrial,
    hasValidSubscription,
    trialEndsAt: subscription.trialEndsAt,
    currentPeriodEnd: subscription.currentPeriodEnd,
  };
}

/**
 * Check if shop is subscribed (simple boolean check)
 */
export async function isShopSubscribed(shop: string): Promise<boolean> {
  const status = await getSubscriptionStatus(shop);
  return status.isActive;
}

/**
 * Activate subscription for a shop
 */
export async function activateSubscription(
  shop: string,
  options: {
    subscriptionId?: string;
    chargeId?: string;
    trialEndsAt?: Date;
    currentPeriodEnd?: Date;
  } = {}
) {
  return await prisma.subscription.upsert({
    where: { shop },
    update: {
      isActive: true,
      subscriptionId: options.subscriptionId,
      chargeId: options.chargeId,
      trialEndsAt: options.trialEndsAt,
      currentPeriodEnd: options.currentPeriodEnd,
      updatedAt: new Date(),
    },
    create: {
      shop,
      isActive: true,
      subscriptionId: options.subscriptionId,
      chargeId: options.chargeId,
      trialEndsAt: options.trialEndsAt,
      currentPeriodEnd: options.currentPeriodEnd,
    },
  });
}

/**
 * Deactivate subscription for a shop
 */
export async function deactivateSubscription(shop: string) {
  return await prisma.subscription.upsert({
    where: { shop },
    update: {
      isActive: false,
      updatedAt: new Date(),
    },
    create: {
      shop,
      isActive: false,
    },
  });
}

/**
 * Start trial for a shop
 */
export async function startTrial(shop: string, trialDays: number = 7) {
  const trialEndsAt = new Date();
  trialEndsAt.setDate(trialEndsAt.getDate() + trialDays);

  return await activateSubscription(shop, {
    trialEndsAt,
  });
}

/**
 * Get all active subscriptions
 */
export async function getActiveSubscriptions() {
  return await prisma.subscription.findMany({
    where: { isActive: true },
    select: {
      shop: true,
      trialEndsAt: true,
      currentPeriodEnd: true,
      subscriptionId: true,
      createdAt: true,
    },
  });
}

/**
 * Clean up expired subscriptions (run this periodically)
 */
export async function cleanupExpiredSubscriptions() {
  const now = new Date();
  
  const expiredSubscriptions = await prisma.subscription.findMany({
    where: {
      isActive: true,
      OR: [
        {
          trialEndsAt: {
            not: null,
            lt: now,
          },
          currentPeriodEnd: null,
        },
        {
          currentPeriodEnd: {
            not: null,
            lt: now,
          },
        },
      ],
    },
  });

  if (expiredSubscriptions.length > 0) {
    await prisma.subscription.updateMany({
      where: {
        id: {
          in: expiredSubscriptions.map(sub => sub.id),
        },
      },
      data: {
        isActive: false,
        updatedAt: now,
      },
    });

    console.log(`Deactivated ${expiredSubscriptions.length} expired subscriptions`);
  }

  return expiredSubscriptions.length;
}

