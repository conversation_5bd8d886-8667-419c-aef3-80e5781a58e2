import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migrateSubscriptionStatus() {
  console.log('🔄 Starting subscription status migration...');
  
  try {
    // Check if status column still exists
    const result = await prisma.$queryRaw`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'subscriptions' 
      AND column_name = 'status'
    `;
    
    if (result.length > 0) {
      console.log('📊 Found old status column, migrating data...');
      
      // Get all subscriptions with old status
      const subscriptions = await prisma.$queryRaw`
        SELECT id, shop, status, "trialEndsAt", "currentPeriodEnd"
        FROM subscriptions
      `;
      
      console.log(`📋 Found ${subscriptions.length} subscriptions to migrate`);
      
      // Update each subscription
      for (const sub of subscriptions) {
        const isActive = ['active', 'trial'].includes(sub.status) && 
          (sub.trialEndsAt === null || new Date(sub.trialEndsAt) > new Date()) &&
          (sub.currentPeriodEnd === null || new Date(sub.currentPeriodEnd) > new Date());
        
        await prisma.subscription.update({
          where: { id: sub.id },
          data: { isActive }
        });
        
        console.log(`✅ Updated ${sub.shop}: ${sub.status} → ${isActive ? 'active' : 'inactive'}`);
      }
      
      // Drop the old status column
      await prisma.$executeRaw`ALTER TABLE subscriptions DROP COLUMN IF EXISTS status`;
      console.log('🗑️ Removed old status column');
      
    } else {
      console.log('✅ Status column already removed, checking data consistency...');
      
      // Just verify the data
      const activeCount = await prisma.subscription.count({
        where: { isActive: true }
      });
      
      const inactiveCount = await prisma.subscription.count({
        where: { isActive: false }
      });
      
      console.log(`📊 Current status: ${activeCount} active, ${inactiveCount} inactive subscriptions`);
    }
    
    console.log('✅ Migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
migrateSubscriptionStatus()
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
