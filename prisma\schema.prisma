// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// Note that some adapters may set a maximum length for the String type by default, please ensure your strings are long
// enough when changing adapters.
// See https://www.prisma.io/docs/orm/reference/prisma-schema-reference#string for more information
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      Bo<PERSON>an   @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  Boolean   @default(false)
  locale        String?
  collaborator  <PERSON><PERSON><PERSON>?  @default(false)
  emailVerified Boolean?  @default(false)
}

// Store subscription and billing information per shop
model Subscription {
  id                String   @id @default(cuid())
  shop              String   @unique
  status            String   // 'trial', 'active', 'expired', 'cancelled', 'pending', 'uninstalled'
  trialEndsAt       DateTime?
  currentPeriodEnd  DateTime?
  subscriptionId    String?  // Shopify subscription ID
  chargeId          String?  // Shopify charge ID
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("subscriptions")
}

// Store global auto-checkout settings per shop
model AutoCheckoutSettings {
  id          String   @id @default(cuid())
  shop        String   @unique
  enabled     Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("auto_checkout_settings")
}

// Store generated checkout links for history and analytics
model GeneratedLink {
  id                String   @id @default(cuid())
  shop              String
  productId         String
  variantId         String
  quantity          Int      @default(1)
  checkoutUrl       String
  cartUrl           String
  directProductUrl  String?
  advancedDirectUrl String?
  productTitle      String?
  variantTitle      String?
  price             String?
  createdAt         DateTime @default(now())

  @@map("generated_links")
  @@index([shop, createdAt])
  @@index([shop, productId])
}

// Store analytics data for checkout link usage
model LinkAnalytics {
  id          String   @id @default(cuid())
  shop        String
  linkId      String?
  linkType    String   // 'checkout', 'cart', 'direct', 'advanced'
  productId   String?
  variantId   String?
  clicked     DateTime @default(now())
  userAgent   String?
  referer     String?
  ipAddress   String?

  @@map("link_analytics")
  @@index([shop, clicked])
  @@index([linkType, clicked])
}
