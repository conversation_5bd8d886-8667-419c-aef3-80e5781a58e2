generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      Boolean   @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  Boolean   @default(false)
  locale        String?
  collaborator  <PERSON><PERSON><PERSON>?  @default(false)
  emailVerified Boolean?  @default(false)
}

model Subscription {
  id               String    @id @default(cuid())
  shop             String    @unique
  status           String
  trialEndsAt      DateTime?
  currentPeriodEnd DateTime?
  subscriptionId   String?
  chargeId         String?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @default(now()) @updatedAt

  @@index([status])
  @@index([trialEndsAt], map: "subscriptions_trial_idx")
  @@map("subscriptions")
}

model AutoCheckoutSettings {
  id        String   @id @default(cuid())
  shop      String   @unique
  enabled   Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@map("auto_checkout_settings")
}

model GeneratedLink {
  id                String   @id @default(cuid())
  shop              String
  productId         String
  variantId         String
  quantity          Int      @default(1)
  checkoutUrl       String
  cartUrl           String
  directProductUrl  String?
  advancedDirectUrl String?
  productTitle      String?
  variantTitle      String?
  price             String?
  createdAt         DateTime @default(now())

  @@index([shop, createdAt])
  @@index([shop, productId])
  @@map("generated_links")
}

model LinkAnalytics {
  id        String   @id @default(cuid())
  shop      String
  linkId    String?
  linkType  String
  productId String?
  variantId String?
  clicked   DateTime @default(now())
  userAgent String?
  referer   String?
  ipAddress String?

  @@index([shop, clicked])
  @@index([linkType, clicked])
  @@map("link_analytics")
}
