import type { ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import db from "../db.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { shop, session, topic } = await authenticate.webhook(request);

  console.log(`Received ${topic} webhook for ${shop}`);

  // Webhook requests can trigger multiple times and after an app has already been uninstalled.
  // If this webhook already ran, the session may have been deleted previously.
  if (session) {
    await db.session.deleteMany({ where: { shop } });
  }

  // Mark subscription as uninstalled but preserve data for potential reinstallation
  try {
    await (db as any).subscription.updateMany({
      where: { shop },
      data: {
        status: 'uninstalled',
        updatedAt: new Date()
      }
    });
    console.log(`Marked subscription as uninstalled for shop: ${shop}`);
  } catch (error) {
    console.error("Error updating subscription on uninstall:", error);
  }

  return new Response();
};
